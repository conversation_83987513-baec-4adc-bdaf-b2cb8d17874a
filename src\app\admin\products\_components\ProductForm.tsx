"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { formatCurrency } from "@/lib/formaters";
import { useState } from "react";
import { addProduct } from "../../_actions/product";
import { useFormState, useFormStatus } from "react-dom";

export default function ProductForm() {

    const [priceInCents, setPriceInCents] = useState<number>()

    const [error, formAction] = useFormState(addProduct, { error: [] })

    console.log(error)

  return (
		<form action={formAction} className='space-y-6'>
			
			<div className='space-y-2'>
				<Label htmlFor='name'>Name</Label>
				<Input type='text' name='name' id='name' required />
                {
                    error.name && <div className='text-destructive'>{error.name}</div>
                }
			</div>
			<div className='space-y-2'>
				<Label htmlFor='priceInCents'>Price In Cents</Label>
				<Input
					type='number'
					name='priceInCents'
					id='priceInCents'
					required
					value={priceInCents}
					onChange={(e) => setPriceInCents(Number(e.target.value) || undefined)}
				/>
				<div className='text-muted-foreground'>{formatCurrency((priceInCents || 0) / 100)}</div>
			</div>

			<div className='space-y-2'>
				<Label htmlFor='description'>Description</Label>
				<Textarea name='description' id='description' required />
			</div>
			<div className='space-y-2'>
				<Label htmlFor='file'>File</Label>
				<Input type='file' name='file' id='file' required />
			</div>
            <div className='space-y-2'>
				<Label htmlFor='image'>Image</Label>
				<Input type='file' name='image' id='image' required />
			</div>

            <SubmitButton />
		</form>
	)
}


const SubmitButton = () => {

    const { pending } = useFormStatus()

    return <Button type="submit" disabled={pending}>{pending ? "Adding..." : "Add Product"}</Button>
}